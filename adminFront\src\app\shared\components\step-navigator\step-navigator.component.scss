@import '../../../@theme/styles/colors';

// 新的配色變數 - 現代藍色系
$step-primary: #3B82F6; // 主要藍色
$step-primary-light: #60A5FA; // 淺藍色
$step-primary-dark: #2563EB; // 深藍色
$step-completed: #10B981; // 完成狀態 - 青綠色
$step-completed-light: #34D399; // 淺青綠色
$step-pending: #F3F4F6; // 待處理背景
$step-pending-text: #9CA3AF; // 待處理文字
$step-pending-border: #E5E7EB; // 待處理邊框

// 漸變色
$step-gradient-primary: linear-gradient(135deg, $step-primary-light 0%, $step-primary 100%);
$step-gradient-completed: linear-gradient(135deg, $step-completed-light 0%, $step-completed 100%);

.step-navigator {
  width: 100%;

  .step-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid $border-light;
    padding-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;

    .step-wrapper {
      display: flex;
      align-items: center;
      position: relative;

      &.clickable {
        cursor: pointer;

        .step-item {
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
          }
        }
      }

      .step-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        min-width: 140px;
        justify-content: center;
        gap: 0.5rem;
        border: 2px solid transparent;

        .step-icon {
          font-size: 1.1rem;
          flex-shrink: 0;
        }

        .step-content {
          display: flex;
          align-items: center;
          flex: 1;

          .step-text {
            display: flex;
            align-items: center;
            gap: 0.25rem;

            .step-number {
              font-weight: 600;
              font-size: 0.9rem;
            }

            .step-label {
              white-space: nowrap;
              font-size: 0.95rem;
            }
          }
        }

        .step-check-icon {
          font-size: 1.1rem;
          margin-left: 0.25rem;
          flex-shrink: 0;
        }

        // 狀態樣式
        &.active {
          background: $step-gradient-primary;
          color: white;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
          border-color: $step-primary;

          .step-icon,
          .step-check-icon {
            color: white;
          }
        }

        &.completed {
          background: $step-gradient-completed;
          color: white;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
          border-color: $step-completed;

          .step-icon,
          .step-check-icon {
            color: white;
          }
        }

        &.pending {
          background-color: $step-pending;
          color: $step-pending-text;
          border-color: $step-pending-border;

          .step-icon {
            color: $step-pending-text;
          }

          &:hover {
            background-color: #F9FAFB;
            border-color: #D1D5DB;
            color: #6B7280;
          }
        }
      }

      .step-connector {
        width: 2.5rem;
        height: 3px;
        background-color: $step-pending-border;
        margin: 0 0.5rem;
        transition: all 0.3s ease;
        border-radius: 1.5px;

        &.completed {
          background: $step-gradient-completed;
        }

        &.active {
          background: $step-gradient-primary;
        }
      }
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .step-navigator {
    .step-nav {
      .step-wrapper {
        .step-item {
          font-size: 0.875rem;
          padding: 0.6rem 1rem;
          min-width: 120px;

          .step-content {
            .step-text {
              .step-label {
                font-size: 0.85rem;
              }
            }
          }
        }

        .step-connector {
          width: 2rem;
          margin: 0 0.25rem;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .step-navigator {
    .step-nav {
      flex-direction: column;
      gap: 1rem;

      .step-wrapper {
        width: 100%;
        justify-content: center;

        .step-connector {
          display: none;
        }

        .step-item {
          width: 100%;
          max-width: 240px;
          padding: 0.8rem 1.5rem;
        }
      }
    }
  }
}

// 深色主題支持
:host-context(.dark-theme) {
  .step-navigator {
    .step-nav {
      border-bottom-color: #374151;

      .step-wrapper {
        .step-item {
          &.pending {
            background-color: #1F2937;
            color: #9CA3AF;
            border-color: #374151;

            &:hover {
              background-color: #374151;
              border-color: #4B5563;
              color: #D1D5DB;
            }
          }

          &.active {
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
          }

          &.completed {
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
          }
        }

        .step-connector {
          background-color: #374151;

          &.completed {
            background: $step-gradient-completed;
          }

          &.active {
            background: $step-gradient-primary;
          }
        }
      }
    }
  }
}

// 動畫效果增強
.step-navigator {
  .step-nav {
    .step-wrapper {
      .step-item {
        &.active {
          animation: pulse-active 2s infinite;
        }

        &.completed {
          .step-check-icon {
            animation: check-bounce 0.6s ease-out;
          }
        }
      }
    }
  }
}

@keyframes pulse-active {

  0%,
  100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }

  50% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.35);
  }
}

@keyframes check-bounce {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}
